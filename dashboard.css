/* Modern CSS Variables for Consistent Theming */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #eff6ff;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 14px;
}

/* Header Styles */
.header {
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo::before {
  content: "🎯";
  font-size: 1.25rem;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.logout-button {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.logout-button:hover {
  background-color: var(--border-light);
  border-color: var(--secondary-color);
}

/* Container and Layout */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

.dashboard-header {
  margin-bottom: 2rem;
}

h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Card Styles */
.card {
  background-color: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-light);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.token-info {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 15px;
  font-family: monospace;
  overflow-x: auto;
  margin-bottom: 15px;
}

.token-expiry {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}

/* Button Styles */
.button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  line-height: 1.5;
}

.button:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.button:active {
  transform: translateY(0);
}

.button.secondary {
  background-color: var(--surface-color);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.button.secondary:hover {
  background-color: var(--background-color);
  border-color: var(--secondary-color);
}

.button.danger {
  background-color: var(--danger-color);
}

.button.danger:hover {
  background-color: var(--danger-hover);
}

.button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Search Styles */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--surface-color);
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-input::placeholder {
  color: var(--text-secondary);
}

.search-loading {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.candidate-search-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-light);
  background-color: var(--background-color);
  flex-shrink: 0;
}

/* Candidate table container with proper scrolling */
#candidateTableContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.candidate-table-wrapper {
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.candidate-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--surface-color);
}



.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 36px;
  font-weight: 500;
  margin: 10px 0;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.not-logged-in {
  text-align: center;
  padding: 50px 0;
}

/* Table Styles */
.assessments-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  background-color: var(--surface-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.candidate-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--surface-color);
}

.assessments-table th,
.assessments-table td,
.candidate-table th,
.candidate-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-light);
  font-size: 0.875rem;
}

.assessments-table th {
  background-color: var(--background-color);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
}

.candidate-table th {
  background-color: var(--background-color);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid var(--border-color);
}

.assessments-table tr:hover,
.candidate-table tr:hover {
  background-color: var(--primary-light);
}

.assessments-table tr:last-child td,
.candidate-table tr:last-child td {
  border-bottom: none;
}

.loading-container {
  text-align: center;
  padding: 30px 0;
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #4a90e2;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

.empty-state {
  text-align: center;
  padding: 30px 0;
  color: #666;
}

.no-results {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
  background-color: var(--background-color);
  border-radius: var(--radius-md);
  margin: 1rem 0;
}

.no-results h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
  font-size: 1.125rem;
  font-weight: 600;
}

.no-results p {
  margin: 0;
  font-size: 0.875rem;
}

.assessment-action {
  margin-right: 8px;
  padding: 5px 10px;
  font-size: 12px;
}

/* Badge Styles */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-active {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.badge-completed {
  background-color: #d1fae5;
  color: #065f46;
}

.badge-pending {
  background-color: #fef3c7;
  color: #92400e;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-light);
  flex-wrap: wrap;
  gap: 10px;
  flex-shrink: 0;
  background-color: var(--surface-color);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.875rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
}

.pagination-text {
  white-space: nowrap;
}

.selected-count-info {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: var(--primary-color);
  white-space: nowrap;
  background-color: var(--primary-light);
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid #bfdbfe;
}

.selected-count-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  margin-right: 0.5rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.items-per-page-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.items-per-page-select {
  padding: 5px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  cursor: pointer;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 10px;
}

.pagination-button {
  padding: 6px 12px;
  font-size: 14px;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-page {
  font-size: 14px;
  color: #666;
}

/* Style for checkboxes with persistent selection */
.candidate-checkbox.persistent-selected {
  accent-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.5);
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

/* Ensure the confirmation modal appears on top of other modals */
#confirmationModal {
  z-index: 1100;
}

.modal-content {
  background-color: var(--surface-color);
  margin: 2rem auto;
  padding: 0;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.confirmation-modal-content {
  max-width: 500px;
  width: 90%;
}

.modal-body {
  padding: 1.5rem;
  font-size: 1rem;
  line-height: 1.6;
}

.checkbox-container {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
  cursor: pointer;
}

.checkbox-container input[type='checkbox'] {
  margin-right: 8px;
  cursor: pointer;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-light);
  background-color: var(--background-color);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-light);
  background-color: var(--background-color);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: var(--border-light);
  color: var(--text-primary);
}

/* Filter Styles */
.filter-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background-color: var(--background-color);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  flex-shrink: 0;
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.75rem;
}

.filter-heading {
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 4rem;
}

.filter-button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--surface-color);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  color: var(--text-primary);
}

.filter-button:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
}

.filter-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Candidate Status Badges */
.candidate-status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
}

.candidate-status-completed {
  background-color: #d1fae5;
  color: #065f46;
}

.candidate-status-started {
  background-color: #fef3c7;
  color: #92400e;
}

.candidate-status-invited {
  background-color: #dbeafe;
  color: #1e40af;
}

.candidate-status-expired {
  background-color: #fee2e2;
  color: #991b1b;
}

/* Candidate Stage Badges */
.candidate-stage {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.candidate-stage-EVA {
  background-color: #d1fae5;
  color: #065f46;
}

.candidate-stage-IFI {
  background-color: #dbeafe;
  color: #1e40af;
}

.candidate-stage-REJ {
  background-color: #fee2e2;
  color: #991b1b;
}

.candidate-stage-NYE {
  background-color: #f1f5f9;
  color: #475569;
}

.delete-button,
.reject-button {
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  margin-right: 5px;
}

.delete-button {
  background-color: #ffebee;
  color: #d32f2f;
}

.delete-button:hover {
  background-color: #ffcdd2;
}

.reject-button {
  background-color: #e8eaf6;
  color: #3949ab;
}

.reject-button:hover {
  background-color: #c5cae9;
}

.candidate-loading {
  text-align: center;
  padding: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.candidate-error {
  color: #d32f2f;
  padding: 20px;
  text-align: center;
  background-color: #ffebee;
  border-radius: 4px;
  margin-bottom: 20px;
}

.no-candidates {
  text-align: center;
  padding: 20px;
  color: #666;
}

.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: var(--background-color);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  flex-shrink: 0;
}

.bulk-selected-actions,
.bulk-all-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.candidate-checkbox {
  width: 1.125rem;
  height: 1.125rem;
  cursor: pointer;
  accent-color: var(--primary-color);
}

.sortable {
  cursor: pointer;
  position: relative;
  padding-right: 1.5rem;
  transition: all 0.2s ease;
}

.sortable:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.sortable::after {
  content: '↕';
  position: absolute;
  right: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.sortable.asc::after {
  content: '↑';
  color: var(--primary-color);
}

.sortable.desc::after {
  content: '↓';
  color: var(--primary-color);
}

/* Progress Bar Styles */
.progress-container {
  display: none;
  margin: 1rem 0;
  background-color: var(--border-light);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.progress-bar {
  height: 1.5rem;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
  width: 0%;
  transition: width 0.3s ease;
  text-align: center;
  line-height: 1.5rem;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.progress-status {
  font-weight: 600;
  color: var(--text-primary);
}

.progress-count {
  font-weight: normal;
}

/* Error States */
.error-message {
  color: var(--danger-color);
  padding: 1rem;
  text-align: center;
  background-color: #fee2e2;
  border-radius: var(--radius-lg);
  margin-bottom: 1rem;
  border: 1px solid #fecaca;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .header {
    padding: 1rem;
  }

  .pagination-container {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .pagination-info {
    justify-content: center;
  }

  .pagination-controls {
    justify-content: center;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .bulk-selected-actions,
  .bulk-all-actions {
    justify-content: center;
  }

  .filter-group {
    justify-content: center;
  }

  .modal-content {
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}

/* Focus States for Accessibility */
.button:focus,
.filter-button:focus,
.candidate-checkbox:focus,
.items-per-page-select:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
